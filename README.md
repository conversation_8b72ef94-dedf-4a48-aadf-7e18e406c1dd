# 🚀 ScreenMonitorMCP - AI'ya Gerçek Zamanlı Görme Yetisi Kazandıran Devrimci MCP Server

> *AI'nın gözleri olun. Ekranın<PERSON><PERSON><PERSON> i<PERSON><PERSON>, anlasın ve etkile<PERSON>im kursun.*

**ScreenMonitorMCP**, <PERSON> ve diğer AI asistanlarına gerçek zamanlı ekran görme, anlama ve etkileşim kurma yetisi kazandıran devrimci bir MCP (Model Context Protocol) sunucusudur. Bu proje, AI'nın sadece metin tabanlı değil, görsel olarak da dünyayı anlayabilmesini sağlar.

## ✨ Neden ScreenMonitorMCP?

Hayal edin: AI asistanınız ekranınızda ne olup bittiğini görebiliyor, önemli değişiklikleri fark ediyor, hatta "Kaydet butonuna tıkla" dediğinizde bunu gerçekten yapabiliyor. İşte tam olarak bunu sağlıyoruz!

## 🎯 <PERSON>rimci Özellikler

### 🧠 **Akıllı İzleme Sistemi**
- **`start_smart_monitoring()`** - AI'ya sürekli görme yetisi kazandırır
- **`get_monitoring_insights()`** - AI destekli akıllı analizler
- **`get_recent_events()`** - Ekran değişikliklerinin geçmişi
- **`stop_smart_monitoring()`** - İzlemeyi durdurur

### 🎯 **Doğal Dil ile UI Etkileşimi**
- **`smart_click()`** - "Kaydet butonuna tıkla" gibi doğal komutlarla etkileşim
- **`extract_text_from_screen()`** - Ekrandaki metinleri OCR ile çıkarır
- **`get_active_application()`** - Aktif uygulamayı tespit eder

### 📸 **Gelişmiş Görsel Analiz**
- **`capture_and_analyze()`** - Ekran görüntüsü alır ve AI ile analiz eder
- **`record_and_analyze()`** - Video kaydı alır ve AI ile analiz eder
- **`query_vision_about_current_view()`** - Mevcut ekran hakkında AI'ya soru sorar

### ⚡ **Sistem Optimizasyonu**
- **`get_system_metrics()`** - Kapsamlı sistem sağlık raporu
- **`get_cache_stats()`** - Önbellek performans istatistikleri
- **`optimize_image()`** - Gelişmiş görüntü optimizasyonu
- **`simulate_input()`** - Klavye ve fare simülasyonu

## 🚀 Hızlı Başlangıç

### 1. Kurulum
```bash
git clone https://github.com/inkbytefo/ScreenMonitorMCP.git
cd ScreenMonitorMCP
pip install -r requirements.txt
```

### 2. Yapılandırma
```bash
cp .env.example .env
# .env dosyasını düzenleyip OpenAI API anahtarınızı ekleyin
```

### 3. Sunucuyu Başlatın
```bash
python main.py
```

### 4. Claude Desktop ile Bağlantı

Claude Desktop'ın `claude_desktop_config.json` dosyasına ekleyin:

```json
{
  "mcpServers": {
    "screen-monitor": {
      "command": "python",
      "args": ["C:/path/to/ScreenMonitorMCP/main.py"],
      "env": {
        "OPENAI_API_KEY": "your-openai-api-key"
      }
    }
  }
}
```

## 💡 Kullanım Örnekleri

```python
# AI'ya sürekli görme yetisi kazandırın
await start_smart_monitoring(triggers=['significant_change', 'error_detected'])

# Doğal dille UI etkileşimi
await smart_click('Save button')
await smart_click('Email input field')

# Ekran hakkında AI'ya soru sorun
await query_vision_about_current_view('Bu sayfada hangi hatalar var?')

# Ekrandaki metni çıkarın
await extract_text_from_screen()
```

## 🎨 Vizyonumuz

**AI'nın sadece konuşmakla kalmayıp, gerçekten görebilmesi ve etkileşim kurabilmesi.**

Gelecekte AI asistanları:
- Ekranınızı izleyerek proaktif yardım sunacak
- Hataları otomatik tespit edip çözüm önerecek
- Karmaşık UI görevlerini otomatik gerçekleştirecek
- Görsel içerikleri anlayıp yorumlayacak

## 🌟 Misyonumuz

**AI ile insan arasındaki görsel iletişim bariyerini kaldırmak.**

Bu proje ile:
- AI'nın görsel dünyayı anlama kapasitesini artırıyoruz
- Kullanıcı deneyimini devrimsel şekilde geliştiriyoruz
- Gelecekteki AI-insan etkileşiminin temellerini atıyoruz

## 🛠 Teknik Özellikler

- **25+ Devrimci Tool** - Kapsamlı AI görme yetenekleri
- **Gerçek Zamanlı İzleme** - Adaptif FPS ile akıllı izleme
- **Çoklu AI Desteği** - OpenAI, OpenRouter ve özel endpoint'ler
- **Gelişmiş OCR** - Tesseract ve EasyOCR desteği
- **Çapraz Platform** - Windows, macOS, Linux desteği
- **Akıllı Önbellek** - Performans optimizasyonu
- **Güvenlik Odaklı** - API anahtarı yönetimi

## 📊 Mevcut Araçlar (25+ Tool)

| Kategori | Araç Sayısı | Açıklama |
|----------|-------------|----------|
| 🧠 **Akıllı İzleme** | 8 | Gerçek zamanlı AI görme sistemi |
| 🎯 **UI Etkileşimi** | 6 | Doğal dil ile ekran kontrolü |
| 📸 **Görsel Analiz** | 5 | AI destekli görüntü/video analizi |
| ⚡ **Sistem Araçları** | 6+ | Performans ve optimizasyon |

## 🤝 Katkıda Bulunun

Bu devrimci projeye katkıda bulunmak ister misiniz?

- 🐛 **Bug Report**: Sorunları bildirin
- 💡 **Feature Request**: Yeni özellik önerileri
- 🔧 **Pull Request**: Kod katkıları
- 📖 **Documentation**: Dokümantasyon iyileştirmeleri

Detaylar için [CONTRIBUTING.md](CONTRIBUTING.md) dosyasını inceleyin.

## 📜 Lisans

Bu proje MIT lisansı altında yayınlanmıştır. Detaylar için [LICENSE](LICENSE) dosyasını inceleyin.

---

**🔥 AI'nın gözleri olmaya hazır mısınız?**

*ScreenMonitorMCP ile AI asistanınız artık sadece dinlemekle kalmayacak, görecek ve etkileşim kuracak!*